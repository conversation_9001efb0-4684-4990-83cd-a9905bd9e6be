/**
 * @fileoverview Routes for spreadsheet agent operations - Single endpoint approach
 */

import express from 'express';
import { SpreadsheetsAgentController } from '../controllers/SpreadsheetsAgentController.js';
import { authenticateToken, rateLimitPerUser } from '../middleware/auth.js';
import { validate } from '../middleware/validation.js';

import Jo<PERSON> from 'joi';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Apply rate limiting
router.use(rateLimitPerUser(100, 60 * 1000)); // 100 requests per minute


/**
 * Validation schema for the single query endpoint
 */
const querySchema = Joi.object({
  query: Joi.string().required().min(1).max(2000).messages({
    'string.empty': 'Query cannot be empty',
    'string.min': 'Query must be at least 1 character long',
    'string.max': 'Query cannot exceed 2000 characters',
    'any.required': 'Query is required'
  }),
  context: Joi.object().optional().description('Optional context for the query')
});

/**
 * @route POST /api/spreadsheets-agent/query
 * @desc Process user query through spreadsheet agent
 * @access Private
 * @body {string} query - Natural language query for spreadsheet operations
 * @body {object} context - Optional context object
 *
 * Examples of supported queries:
 * - "Show me all my spreadsheets"
 * - "Create a new spreadsheet called 'Project Tasks' with sheets for tasks and resources"
 * - "Read data from my budget spreadsheet"
 * - "Update cell A1 in spreadsheet 1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms to 'Task Name'"
 * - "Get details of my project tracker spreadsheet"
 * - "Add a new row with data ['Task 1', 'In Progress', 'John'] to my task sheet"
 */
router.post('/query',
  validate(querySchema),
  SpreadsheetsAgentController.processQuery
);

export default router;
